#!/bin/bash

# Script de despliegue automático para Lambda spreadsheet_generator
# Autor: Generado automáticamente
# Fecha: $(date)

set -e  # Salir si cualquier comando falla

# Configuración
FUNCTION_ARN="arn:aws:lambda:us-east-1:165354057769:function:spreadsheet_generator"
ZIP_FILE="planilla-lambda.zip"
LAMBDA_DIR="lambda"
TEMP_FILES=()

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Función de limpieza
cleanup() {
    log "Iniciando limpieza..."
    for file in "${TEMP_FILES[@]}"; do
        if [ -f "$file" ]; then
            rm -f "$file"
            log "Eliminado: $file"
        fi
    done
    success "Limpieza completada"
}

# Trap para limpieza en caso de error o salida
trap cleanup EXIT

# Verificar que estamos en el directorio correcto
if [ ! -d "$LAMBDA_DIR" ] || [ ! -f "$LAMBDA_DIR/lambda-handler.js" ]; then
    error "No se encontró el directorio lambda o lambda-handler.js. Ejecuta desde el directorio raíz del proyecto."
    exit 1
fi

# Verificar que AWS CLI está instalado y configurado
if ! command -v aws &> /dev/null; then
    error "AWS CLI no está instalado"
    exit 1
fi

# Verificar credenciales AWS
if ! aws sts get-caller-identity &> /dev/null; then
    error "AWS CLI no está configurado correctamente"
    exit 1
fi

log "Iniciando despliegue de Lambda..."

# Paso 1: Crear archivo ZIP
log "Creando archivo ZIP..."
if zip -r "$ZIP_FILE" lambda-handler.js node_modules/ package.json > /dev/null 2>&1; then
    success "Archivo ZIP creado: $ZIP_FILE"
    TEMP_FILES+=("$ZIP_FILE")
else
    error "Error al crear archivo ZIP"
    exit 1
fi

# Verificar tamaño del ZIP
ZIP_SIZE=$(du -h "$ZIP_FILE" | cut -f1)
log "Tamaño del archivo: $ZIP_SIZE"

# Paso 2: Actualizar función Lambda
log "Actualizando función Lambda..."
if aws lambda update-function-code \
    --function-name "$FUNCTION_ARN" \
    --zip-file "fileb://$ZIP_FILE" \
    --output table > /dev/null 2>&1; then
    success "Función Lambda actualizada exitosamente"
else
    error "Error al actualizar función Lambda"
    exit 1
fi

# Paso 3: Verificar estado de la función
log "Verificando estado de la función..."
sleep 2  # Esperar un momento para que se procese

FUNCTION_STATE=$(aws lambda get-function \
    --function-name "$FUNCTION_ARN" \
    --query 'Configuration.LastUpdateStatus' \
    --output text)

if [ "$FUNCTION_STATE" = "Successful" ]; then
    success "Función en estado: $FUNCTION_STATE"
elif [ "$FUNCTION_STATE" = "InProgress" ]; then
    warning "Función en estado: $FUNCTION_STATE - Esperando..."
    
    # Esperar hasta que termine la actualización
    for i in {1..30}; do
        sleep 2
        FUNCTION_STATE=$(aws lambda get-function \
            --function-name "$FUNCTION_ARN" \
            --query 'Configuration.LastUpdateStatus' \
            --output text)
        
        if [ "$FUNCTION_STATE" = "Successful" ]; then
            success "Función actualizada correctamente"
            break
        elif [ "$FUNCTION_STATE" = "Failed" ]; then
            error "La actualización de la función falló"
            exit 1
        fi
        
        if [ $i -eq 30 ]; then
            error "Timeout esperando actualización de función"
            exit 1
        fi
    done
else
    error "Estado inesperado de función: $FUNCTION_STATE"
    exit 1
fi

# Paso 4: Obtener información final
log "Obteniendo información final..."
FUNCTION_INFO=$(aws lambda get-function \
    --function-name "$FUNCTION_ARN" \
    --query 'Configuration.{LastModified:LastModified,CodeSize:CodeSize,Runtime:Runtime}' \
    --output table)

echo ""
success "🚀 DESPLIEGUE COMPLETADO EXITOSAMENTE"
echo ""
echo "📋 Información de la función:"
echo "$FUNCTION_INFO"
echo ""
echo "🔗 ARN: $FUNCTION_ARN"
echo ""

# Mostrar funcionalidades implementadas
echo "✨ Funcionalidades implementadas:"
echo "   • Soporte para logo en base64 (PNG)"
echo "   • Manejo de logo vacío con espacio apropiado"
echo "   • Alineación perfecta de tablas con headers"
echo "   • Márgenes optimizados para máximo aprovechamiento"
echo ""

success "Despliegue finalizado. Archivos temporales limpiados."
