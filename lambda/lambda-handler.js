const AWS = require('aws-sdk');
const PdfPrinter = require('pdfmake');

const s3 = new AWS.S3();

// Configuración de fuentes simplificada
const fonts = {
  Helvetica: {
    normal: 'Helvetica',
    bold: 'Helvetica-Bold',
  }
};

// Función para generar el PDF (extraída del archivo original)
function generarPlanillaPDF(planillaData, logoBase64) {
  return new Promise((resolve, reject) => {
    const printer = new PdfPrinter(fonts);

    // Calcular anchos uniformes para alineación perfecta
    const pageWidth = 842 - 40; // A4 landscape menos márgenes
    const headerWidths = [100, pageWidth - 250, 150]; // Logo, título, info
    const infoWidths = [150, 300, pageWidth - 450]; // Consecutivo, mensajero, firma

    // Ajustar anchos de tabla para alineación perfecta con header y márgenes
    const totalTableWidth = pageWidth;
    const tableWidths = [55, 55, 75, 55, 55, 55, 75, 28, 18, 38, 73]; // Total: 582
    // Ajustar para que sume exactamente el ancho total disponible
    const adjustedTableWidths = tableWidths.map(w => (w / 582) * totalTableWidth);

    // Preparar contenido del logo
    let logoContent;
    if (logoBase64 && logoBase64.trim() !== '') {
      logoContent = {
        image: `data:image/png;base64,${logoBase64}`,
        width: 80,
        height: 60,
        alignment: 'center',
        margin: [5, 5]
      };
    } else {
      logoContent = { text: '', margin: [5, 30] }; // Espacio vacío con altura similar
    }

    const docDefinition = {
      pageSize: 'A4',
      pageOrientation: 'landscape',
      pageMargins: [20, 20, 20, 20],
      defaultStyle: {
        font: 'Helvetica'
      },
      content: [
        // Encabezado
        {
          table: {
            widths: headerWidths,
            body: [
              [
                logoContent,
                { text: 'PLANILLA DE DISTRIBUCIÓN INTERNA 2022', style: 'title', border: [true, true, true, true] },
                {
                  table: {
                    widths: ['*'],
                    body: [
                      [{ text: `Código: ${planillaData.codigo}`, style: 'headerInfo' }],
                      [{ text: `Versión: ${planillaData.version}`, style: 'headerInfo' }],
                      [{ text: `Fecha: ${planillaData.fecha}`, style: 'headerInfo' }]
                    ]
                  },
                  border: [true, true, true, true]
                }
              ]
            ]
          },
          layout: 'noBorders'
        },
        { text: '\n' },

        // Información del consecutivo y mensajero
        {
          table: {
            widths: infoWidths,
            body: [
              [
                { text: `Consecutivo: ${planillaData.consecutivo}`, style: 'info' },
                { text: `Mensajero: ${planillaData.mensajero}`, style: 'info' },
                { text: 'Firma: ________________________________', style: 'info' }
              ]
            ]
          },
          layout: {
            hLineWidth: () => 1,
            vLineWidth: () => 1
          }
        },
        { text: '\n' },

        // Tabla principal
        {
          table: {
            headerRows: 1,
            widths: adjustedTableWidths,
            body: [
              // Encabezados
              [
                { text: 'FECHA DE\nRADICADO\nDD/MM/AA', style: 'tableHeader' },
                { text: 'NUMERO DE\nRADICADO', style: 'tableHeader' },
                { text: 'ASUNTO', style: 'tableHeader' },
                { text: 'ENTIDAD', style: 'tableHeader' },
                { text: 'REMITENTE', style: 'tableHeader' },
                { text: 'SECCION', style: 'tableHeader' },
                { text: 'DESTINATARIO', style: 'tableHeader' },
                { text: 'FOLIOS', style: 'tableHeader' },
                { text: 'CC', style: 'tableHeader' },
                { text: 'PASA A', style: 'tableHeader' },
                { text: 'FIRMA Y SELLO\nDE RECIBIDO', style: 'tableHeader' }
              ],
              // Datos
              ...planillaData.registros.map(registro => [
                { text: registro.fechaRadicado, style: 'tableCell' },
                { text: registro.numeroRadicado, style: 'tableCell' },
                { text: registro.asunto, style: 'tableCell' },
                { text: registro.entidad, style: 'tableCell' },
                { text: registro.remitente, style: 'tableCell' },
                { text: registro.seccion, style: 'tableCell' },
                { text: registro.destinatario, style: 'tableCell' },
                { text: registro.folios, style: 'tableCell', alignment: 'center' },
                { text: registro.cc, style: 'tableCell', alignment: 'center' },
                { text: registro.pasaA, style: 'tableCell' },
                { text: registro.firma, style: 'tableCell' }
              ])
            ]
          },
          layout: {
            hLineWidth: () => 1,
            vLineWidth: () => 1,
            hLineColor: () => '#000000',
            vLineColor: () => '#000000'
          }
        }
      ],
      styles: {
        logo: {
          fontSize: 12,
          alignment: 'center',
          margin: [5, 10]
        },
        title: {
          fontSize: 14,
          bold: true,
          alignment: 'center',
          margin: [5, 15]
        },
        headerInfo: {
          fontSize: 9,
          margin: [5, 2]
        },
        info: {
          fontSize: 10,
          margin: [5, 5]
        },
        tableHeader: {
          fontSize: 8,
          bold: true,
          alignment: 'center',
          margin: [2, 2],
          fillColor: '#f0f0f0'
        },
        tableCell: {
          fontSize: 7,
          margin: [2, 2]
        }
      }
    };

    const pdfDoc = printer.createPdfKitDocument(docDefinition);
    const chunks = [];
    
    pdfDoc.on('data', chunk => chunks.push(chunk));
    pdfDoc.on('end', () => resolve(Buffer.concat(chunks)));
    pdfDoc.on('error', reject);
    
    pdfDoc.end();
  });
}

// Handler principal de Lambda
exports.handler = async (event) => {
  try {
    // Parsear el body si viene de Lambda URL
    let requestData;
    if (event.body) {
      // Lambda URL - el body viene como string
      requestData = JSON.parse(event.body);
    } else {
      // Invocación directa - el evento es el objeto completo
      requestData = event;
    }

    // Datos por defecto o desde el evento
    const planillaData = requestData.planillaData || {
      codigo: '25897',
      version: '01.05',
      fecha: new Date().toISOString().slice(0, 19).replace('T', ' '),
      consecutivo: '0009',
      mensajero: 'Alexandra Yohana Valencia Florez',
      registros: [
        {
          fechaRadicado: '25/02/25\n09:39:15',
          numeroRadicado: '20221129',
          asunto: 'Prueba',
          entidad: 'A.R.L Sura',
          remitente: 'A.R.L Sura',
          seccion: 'Gerencia',
          destinatario: 'Martha María\nVásquez Correa',
          folios: '2',
          cc: '',
          pasaA: '',
          firma: ''
        }
      ]
    };

    // Obtener logo en base64 (opcional)
    const logoBase64 = requestData.logoBase64 || '';

    // Generar PDF
    const pdfBuffer = await generarPlanillaPDF(planillaData, logoBase64);
    
    // Subir a S3 (opcional)
    if (event.uploadToS3) {
      const bucketName = event.bucketName || 'mi-bucket-planillas';
      const fileName = `planilla_${Date.now()}.pdf`;
      
      await s3.upload({
        Bucket: bucketName,
        Key: fileName,
        Body: pdfBuffer,
        ContentType: 'application/pdf'
      }).promise();
      
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: 'PDF generado y subido exitosamente',
          s3Key: fileName,
          bucket: bucketName
        })
      };
    }
    
    // Retornar PDF como base64
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="planilla_distribucion.pdf"'
      },
      body: pdfBuffer.toString('base64'),
      isBase64Encoded: true
    };
    
  } catch (error) {
    console.error('Error generando PDF:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Error interno del servidor',
        message: error.message
      })
    };
  }
};
