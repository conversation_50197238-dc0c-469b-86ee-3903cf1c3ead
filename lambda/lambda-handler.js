const AWS = require('aws-sdk');
const PdfPrinter = require('pdfmake');

const s3 = new AWS.S3();

// Configuración de fuentes simplificada
const fonts = {
  Helvetica: {
    normal: 'Helvetica',
    bold: 'Helvetica-Bold',
  }
};

// Función para generar el PDF (extraída del archivo original)
function generarPlanillaPDF(planillaData) {
  return new Promise((resolve, reject) => {
    const printer = new PdfPrinter(fonts);

    const docDefinition = {
      pageSize: 'A4',
      pageOrientation: 'landscape',
      pageMargins: [20, 20, 20, 20],
      defaultStyle: {
        font: 'Helvetica'
      },
      content: [
        // Encabezado
        {
          table: {
            widths: [100, '*', 150],
            body: [
              [
                { text: '[LOGO]', style: 'logo', border: [true, true, true, true] },
                { text: 'PLANILLA DE DISTRIBUCIÓN INTERNA 2022', style: 'title', border: [true, true, true, true] },
                {
                  table: {
                    widths: ['*'],
                    body: [
                      [{ text: `Código: ${planillaData.codigo}`, style: 'headerInfo' }],
                      [{ text: `Versión: ${planillaData.version}`, style: 'headerInfo' }],
                      [{ text: `Fecha: ${planillaData.fecha}`, style: 'headerInfo' }]
                    ]
                  },
                  border: [true, true, true, true]
                }
              ]
            ]
          },
          layout: 'noBorders'
        },
        { text: '\n' },
        
        // Información del consecutivo y mensajero
        {
          table: {
            widths: [150, 300, '*'],
            body: [
              [
                { text: `Consecutivo: ${planillaData.consecutivo}`, style: 'info' },
                { text: `Mensajero: ${planillaData.mensajero}`, style: 'info' },
                { text: 'Firma: ________________________________', style: 'info' }
              ]
            ]
          },
          layout: {
            hLineWidth: () => 1,
            vLineWidth: () => 1
          }
        },
        { text: '\n' },
        
        // Tabla principal
        {
          table: {
            headerRows: 1,
            widths: [60, 60, 80, 60, 60, 60, 80, 30, 20, 40, 80],
            body: [
              // Encabezados
              [
                { text: 'FECHA DE\nRADICADO\nDD/MM/AA', style: 'tableHeader' },
                { text: 'NUMERO DE\nRADICADO', style: 'tableHeader' },
                { text: 'ASUNTO', style: 'tableHeader' },
                { text: 'ENTIDAD', style: 'tableHeader' },
                { text: 'REMITENTE', style: 'tableHeader' },
                { text: 'SECCION', style: 'tableHeader' },
                { text: 'DESTINATARIO', style: 'tableHeader' },
                { text: 'FOLIOS', style: 'tableHeader' },
                { text: 'CC', style: 'tableHeader' },
                { text: 'PASA A', style: 'tableHeader' },
                { text: 'FIRMA Y SELLO\nDE RECIBIDO', style: 'tableHeader' }
              ],
              // Datos
              ...planillaData.registros.map(registro => [
                { text: registro.fechaRadicado, style: 'tableCell' },
                { text: registro.numeroRadicado, style: 'tableCell' },
                { text: registro.asunto, style: 'tableCell' },
                { text: registro.entidad, style: 'tableCell' },
                { text: registro.remitente, style: 'tableCell' },
                { text: registro.seccion, style: 'tableCell' },
                { text: registro.destinatario, style: 'tableCell' },
                { text: registro.folios, style: 'tableCell', alignment: 'center' },
                { text: registro.cc, style: 'tableCell', alignment: 'center' },
                { text: registro.pasaA, style: 'tableCell' },
                { text: registro.firma, style: 'tableCell' }
              ])
            ]
          },
          layout: {
            hLineWidth: () => 1,
            vLineWidth: () => 1,
            hLineColor: () => '#000000',
            vLineColor: () => '#000000'
          }
        }
      ],
      styles: {
        logo: {
          fontSize: 12,
          alignment: 'center',
          margin: [5, 10]
        },
        title: {
          fontSize: 14,
          bold: true,
          alignment: 'center',
          margin: [5, 15]
        },
        headerInfo: {
          fontSize: 9,
          margin: [5, 2]
        },
        info: {
          fontSize: 10,
          margin: [5, 5]
        },
        tableHeader: {
          fontSize: 8,
          bold: true,
          alignment: 'center',
          margin: [2, 2],
          fillColor: '#f0f0f0'
        },
        tableCell: {
          fontSize: 7,
          margin: [2, 2]
        }
      }
    };

    const pdfDoc = printer.createPdfKitDocument(docDefinition);
    const chunks = [];
    
    pdfDoc.on('data', chunk => chunks.push(chunk));
    pdfDoc.on('end', () => resolve(Buffer.concat(chunks)));
    pdfDoc.on('error', reject);
    
    pdfDoc.end();
  });
}

// Handler principal de Lambda
exports.handler = async (event) => {
  try {
    console.log('🔍 Evento completo recibido:', JSON.stringify(event, null, 2));
    console.log('📊 Tipo de evento:', typeof event);
    console.log('📋 Claves del evento:', Object.keys(event));

    // Datos por defecto o desde el evento
    const planillaData = event.planillaData || {
      codigo: '25897',
      version: '01.05',
      fecha: new Date().toISOString().slice(0, 19).replace('T', ' '),
      consecutivo: '0009',
      mensajero: 'Alexandra Yohana Valencia Florez',
      registros: [
        {
          fechaRadicado: '25/02/25\n09:39:15',
          numeroRadicado: '20221129',
          asunto: 'Prueba',
          entidad: 'A.R.L Sura',
          remitente: 'A.R.L Sura',
          seccion: 'Gerencia',
          destinatario: 'Martha María\nVásquez Correa',
          folios: '2',
          cc: '',
          pasaA: '',
          firma: ''
        }
      ]
    };

    console.log('📊 Número de registros a procesar:', planillaData.registros.length);
    console.log('📋 Registros a procesar:');
    planillaData.registros.forEach((registro, index) => {
      console.log(`  ${index + 1}. ${registro.numeroRadicado} - ${registro.asunto}`);
    });

    // Generar PDF
    const pdfBuffer = await generarPlanillaPDF(planillaData);
    
    // Subir a S3 (opcional)
    if (event.uploadToS3) {
      const bucketName = event.bucketName || 'mi-bucket-planillas';
      const fileName = `planilla_${Date.now()}.pdf`;
      
      await s3.upload({
        Bucket: bucketName,
        Key: fileName,
        Body: pdfBuffer,
        ContentType: 'application/pdf'
      }).promise();
      
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: 'PDF generado y subido exitosamente',
          s3Key: fileName,
          bucket: bucketName
        })
      };
    }
    
    // Retornar PDF como base64
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="planilla_distribucion.pdf"'
      },
      body: pdfBuffer.toString('base64'),
      isBase64Encoded: true
    };
    
  } catch (error) {
    console.error('Error generando PDF:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Error interno del servidor',
        message: error.message
      })
    };
  }
};
