const AWS = require('aws-sdk');
const PdfPrinter = require('pdfmake');

const s3 = new AWS.S3();

// Configuración de fuentes simplificada
const fonts = {
  Helvetica: {
    normal: 'Helvetica',
    bold: 'Helvetica-Bold',
  }
};

// Función para generar el PDF (extraída del archivo original)
function generarPlanillaPDF(planillaData, logoBase64) {
  return new Promise((resolve, reject) => {
    const printer = new PdfPrinter(fonts);

    // Usar anchos mixtos para distribución óptima del espacio
    const headerWidths = [80, '*', 120]; // Logo, título, info
    const infoWidths = [120, '*', 180]; // Consecutivo, mensajero, firma

    // Anchos de tabla usando combinación de fijos y relativos
    const tableWidths = [
      55,    // FECHA DE RADICADO (fijo)
      55,    // NUMERO DE RADICADO (fijo)
      '*',   // ASUNTO (flexible)
      60,    // ENTIDAD (fijo)
      60,    // REMITENTE (fijo)
      50,    // SECCION (fijo)
      '*',   // DESTINATARIO (flexible)
      30,    // FOLIOS (fijo)
      20,    // CC (fijo)
      40,    // PASA A (fijo)
      '*'    // FIRMA Y SELLO (flexible)
    ];

    // Preparar contenido del logo
    let logoContent;
    if (logoBase64 && logoBase64.trim() !== '') {
      logoContent = {
        image: `data:image/png;base64,${logoBase64}`,
        width: 70,
        height: 50,
        alignment: 'center',
        margin: [2, 2]
      };
    } else {
      logoContent = { text: '', margin: [2, 20] }; // Espacio vacío con altura similar
    }

    const docDefinition = {
      pageSize: 'A4',
      pageOrientation: 'landscape',
      pageMargins: [10, 10, 10, 10], // Márgenes mínimos
      defaultStyle: {
        font: 'Helvetica'
      },
      content: [
        // Encabezado
        {
          table: {
            widths: headerWidths,
            body: [
              [
                logoContent,
                { text: 'PLANILLA DE DISTRIBUCIÓN INTERNA 2022', style: 'title', border: [true, true, true, true] },
                {
                  table: {
                    widths: ['*'],
                    body: [
                      [{ text: `Código: ${planillaData.codigo}`, style: 'headerInfo' }],
                      [{ text: `Versión: ${planillaData.version}`, style: 'headerInfo' }],
                      [{ text: `Fecha: ${planillaData.fecha}`, style: 'headerInfo' }]
                    ]
                  },
                  border: [true, true, true, true]
                }
              ]
            ]
          },
          layout: 'noBorders'
        },
        { text: '\n' },

        // Información del consecutivo y mensajero
        {
          table: {
            widths: infoWidths,
            body: [
              [
                { text: `Consecutivo: ${planillaData.consecutivo}`, style: 'info' },
                { text: `Mensajero: ${planillaData.mensajero}`, style: 'info' },
                { text: 'Firma: ________________________________', style: 'info' }
              ]
            ]
          },
          layout: {
            hLineWidth: () => 1,
            vLineWidth: () => 1
          }
        },
        { text: '\n' },

        // Tabla principal
        {
          table: {
            headerRows: 1,
            widths: tableWidths,
            body: [
              // Encabezados
              [
                { text: 'FECHA DE\nRADICADO\nDD/MM/AA', style: 'tableHeader' },
                { text: 'NUMERO DE\nRADICADO', style: 'tableHeader' },
                { text: 'ASUNTO', style: 'tableHeader' },
                { text: 'ENTIDAD', style: 'tableHeader' },
                { text: 'REMITENTE', style: 'tableHeader' },
                { text: 'SECCION', style: 'tableHeader' },
                { text: 'DESTINATARIO', style: 'tableHeader' },
                { text: 'FOLIOS', style: 'tableHeader' },
                { text: 'CC', style: 'tableHeader' },
                { text: 'PASA A', style: 'tableHeader' },
                { text: 'FIRMA Y SELLO\nDE RECIBIDO', style: 'tableHeader' }
              ],
              // Datos
              ...planillaData.registros.map(registro => [
                { text: registro.fechaRadicado, style: 'tableCell' },
                { text: registro.numeroRadicado, style: 'tableCell' },
                { text: registro.asunto, style: 'tableCell' },
                { text: registro.entidad, style: 'tableCell' },
                { text: registro.remitente, style: 'tableCell' },
                { text: registro.seccion, style: 'tableCell' },
                { text: registro.destinatario, style: 'tableCell' },
                { text: registro.folios, style: 'tableCell', alignment: 'center' },
                { text: registro.cc, style: 'tableCell', alignment: 'center' },
                { text: registro.pasaA, style: 'tableCell' },
                { text: registro.firma, style: 'tableCell' }
              ])
            ]
          },
          layout: {
            hLineWidth: () => 1,
            vLineWidth: () => 1,
            hLineColor: () => '#000000',
            vLineColor: () => '#000000'
          }
        }
      ],
      styles: {
        logo: {
          fontSize: 12,
          alignment: 'center',
          margin: [2, 5]
        },
        title: {
          fontSize: 14,
          bold: true,
          alignment: 'center',
          margin: [2, 10]
        },
        headerInfo: {
          fontSize: 9,
          margin: [3, 1]
        },
        info: {
          fontSize: 10,
          margin: [3, 3]
        },
        tableHeader: {
          fontSize: 7,
          bold: true,
          alignment: 'center',
          margin: [1, 1],
          fillColor: '#f0f0f0'
        },
        tableCell: {
          fontSize: 6,
          margin: [1, 1]
        }
      }
    };

    const pdfDoc = printer.createPdfKitDocument(docDefinition);
    const chunks = [];
    
    pdfDoc.on('data', chunk => chunks.push(chunk));
    pdfDoc.on('end', () => resolve(Buffer.concat(chunks)));
    pdfDoc.on('error', reject);
    
    pdfDoc.end();
  });
}

// Handler principal de Lambda
exports.handler = async (event) => {
  try {
    // Parsear el body si viene de Lambda URL
    let requestData;
    if (event.body) {
      // Lambda URL - el body viene como string
      requestData = JSON.parse(event.body);
    } else {
      // Invocación directa - el evento es el objeto completo
      requestData = event;
    }

    // Datos por defecto o desde el evento
    const planillaData = requestData.planillaData || {
      codigo: '25897',
      version: '01.05',
      fecha: new Date().toISOString().slice(0, 19).replace('T', ' '),
      consecutivo: '0009',
      mensajero: 'Alexandra Yohana Valencia Florez',
      registros: [
        {
          fechaRadicado: '25/02/25\n09:39:15',
          numeroRadicado: '20221129',
          asunto: 'Prueba',
          entidad: 'A.R.L Sura',
          remitente: 'A.R.L Sura',
          seccion: 'Gerencia',
          destinatario: 'Martha María\nVásquez Correa',
          folios: '2',
          cc: '',
          pasaA: '',
          firma: ''
        }
      ]
    };

    // Obtener logo en base64 (opcional)
    const logoBase64 = requestData.logoBase64 || '';

    // Generar PDF
    const pdfBuffer = await generarPlanillaPDF(planillaData, logoBase64);
    
    // Subir a S3 (opcional)
    if (event.uploadToS3) {
      const bucketName = event.bucketName || 'mi-bucket-planillas';
      const fileName = `planilla_${Date.now()}.pdf`;
      
      await s3.upload({
        Bucket: bucketName,
        Key: fileName,
        Body: pdfBuffer,
        ContentType: 'application/pdf'
      }).promise();
      
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: 'PDF generado y subido exitosamente',
          s3Key: fileName,
          bucket: bucketName
        })
      };
    }
    
    // Retornar PDF como base64
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="planilla_distribucion.pdf"'
      },
      body: pdfBuffer.toString('base64'),
      isBase64Encoded: true
    };
    
  } catch (error) {
    console.error('Error generando PDF:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Error interno del servidor',
        message: error.message
      })
    };
  }
};
