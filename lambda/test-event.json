{"planillaData": {"codigo": "25897", "version": "01.05", "fecha": "2025-07-09 10:25:15", "consecutivo": "0009", "mensajero": "<PERSON> Florez", "registros": [{"fechaRadicado": "09/07/25\n10:39:15", "numeroRadicado": "20250709001", "asunto": "Prueba Lambda AWS", "entidad": "Empresa Test", "remitente": "Departamento IT", "seccion": "Gerencia", "destinatario": "<PERSON>", "folios": "3", "cc": "", "pasaA": "", "firma": ""}, {"fechaRadicado": "09/07/25\n11:15:30", "numeroRadicado": "20250709002", "asunto": "Documento de seguimiento", "entidad": "Proveedor ABC", "remitente": "<PERSON>", "seccion": "Compras", "destinatario": "<PERSON>", "folios": "1", "cc": "X", "pasaA": "Archivo", "firma": ""}]}, "uploadToS3": false}